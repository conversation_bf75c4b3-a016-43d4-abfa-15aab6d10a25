'use client'

import { SyntheticEvent, useCallback, useMemo, useState } from 'react'

import type { UserProfile } from '@/modules/auth/types/auth-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Edit, Plus } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormInput, FormSelect, FormTextarea } from '@/components/form-inputs'

import { useRiskForm } from '../hooks/use-risk'
import {
  AdditionalRiskOptionsEnum,
  riskSchema,
  smokerOptions,
  SmokerStatus,
  type AdditionalRisk,
  type RiskFormInputs,
} from '../types/risk-schema'
import { AdditionalRiskModal } from './additional-risk-modal'

interface RiskFormProps {
  initialData?: UserProfile | null
  onComplete?: (data: RiskFormInputs) => Promise<void>
}

export function RiskForm({ initialData, onComplete }: RiskFormProps) {
  const t = useTranslations()

  const [additionalRisks, setAdditionalRisks] = useState<AdditionalRisk[]>(initialData?.additionalRisks || [])
  const [editingRisk, setEditingRisk] = useState<AdditionalRisk | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const { executeAsync: updateUserRisk, isPending: isSubmitting } = useRiskForm()

  const getSmokerStatus = useMemo(() => {
    switch (initialData?.smoker) {
      case 0:
        return SmokerStatus.NO
      case 50:
        return SmokerStatus.SOMETIMES
      case 100:
        return SmokerStatus.YES
      default:
        return SmokerStatus.UNKNOWN
    }
  }, [initialData])

  const form = useForm<RiskFormInputs>({
    resolver: zodResolver(riskSchema),
    defaultValues: {
      weight: initialData?.weight || undefined,
      height: initialData?.height || undefined,
      smoker: getSmokerStatus,
      healthInfo: initialData?.healthInfo || '',
      // additionalRisks: additionalRisks,
    },
  })

  const healthInfo = form.watch('healthInfo')
  const healthInfoLength = healthInfo?.length || 0

  const startEditRisk = useCallback(
    (e: SyntheticEvent) => {
      const risk = additionalRisks.find((risk) => risk.id === (e.currentTarget as HTMLButtonElement).dataset.id)
      if (risk) {
        setEditingRisk(risk)
        setIsModalOpen(true)
      }
    },
    [additionalRisks]
  )

  const startAddRisk = useCallback(() => {
    setEditingRisk(null)
    setIsModalOpen(true)
  }, [])

  const addRisk = useCallback(
    (risk: AdditionalRisk) => {
      const updatedRisks = [...additionalRisks, risk]
      setAdditionalRisks(updatedRisks)
      setEditingRisk(null) // Clear editing state
      setIsModalOpen(false) // Close modal
      // form.setValue('additionalRisks', updatedRisks)
    },
    [additionalRisks]
  )

  const editRisk = useCallback(
    (oldRisk: AdditionalRisk, newRisk: AdditionalRisk) => {
      const updatedRisks = additionalRisks.map((risk) => (risk.id === oldRisk.id ? newRisk : risk))
      setAdditionalRisks(updatedRisks)
      setEditingRisk(null) // Clear editing state
      setIsModalOpen(false) // Close modal
      // form.setValue('additionalRisks', updatedRisks)
    },
    [additionalRisks]
  )

  const removeRisk = useCallback(
    (riskToRemove: AdditionalRisk) => {
      const updatedRisks = additionalRisks.filter((risk) => risk.id !== riskToRemove.id)
      setAdditionalRisks(updatedRisks)
      setEditingRisk(null) // Clear editing state
      setIsModalOpen(false) // Close modal
      // form.setValue('additionalRisks', updatedRisks)
    },
    [additionalRisks]
  )

  const handleModalClose = useCallback((open: boolean) => {
    setIsModalOpen(open)
    if (!open) {
      setEditingRisk(null) // Clear editing state when modal closes
    }
  }, [])

  const onSubmit = useCallback(
    async (data: RiskFormInputs) => {
      if (onComplete) {
        // If onComplete is provided, use it instead of the default API call
        await onComplete(data)
      } else {
        // Default behavior - call the API directly
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { additionalRisks: formAdditionalRisks, ...apiData } = data
        await updateUserRisk({
          ...apiData,
        })
      }
    },
    [onComplete, updateUserRisk]
  )

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('RISK.EDIT.PHYSICAL_MEASUREMENTS_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormInput<RiskFormInputs>
                      name="weight"
                      label="RISK.EDIT.WEIGHT_FIELD.LABEL"
                      type="number"
                      min={30}
                      max={300}
                      suffix="kg"
                      placeholder="Weight"
                    />

                    <FormInput<RiskFormInputs>
                      name="height"
                      label="RISK.EDIT.HEIGHT_FIELD.LABEL"
                      type="number"
                      min={100}
                      max={250}
                      suffix="cm"
                      placeholder="Height"
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">{t('RISK.EDIT.LIFESTYLE_FIELDSET.TITLE')}</h3>
                  <div className="space-y-4">
                    <FormSelect<RiskFormInputs>
                      name="smoker"
                      label="RISK.EDIT.SMOKER_FIELD.LABEL"
                      options={smokerOptions}
                      placeholder="Smoker"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">{t('RISK.EDIT.HEALTH_INFO_FIELDSET.TITLE')}</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <FormTextarea<RiskFormInputs>
                        name="healthInfo"
                        label="RISK.EDIT.HEALTH_INFO_FIELD.LABEL"
                        placeholder="Text..."
                        maxLength={255}
                        rows={6}
                      />
                      <div className="text-sm text-gray-500 text-right">{healthInfoLength} / 255</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="px-3"
                        onClick={startAddRisk}
                        disabled={additionalRisks.length >= Object.values(AdditionalRiskOptionsEnum).length}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        {t('RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.ADD_BUTTON.TEXT')}
                      </Button>
                    </div>
                    {additionalRisks.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {additionalRisks.map((risk, index) => (
                          <Badge
                            key={risk.id || index}
                            variant="secondary"
                            className="flex items-center gap-2 px-3 py-1"
                          >
                            <span>
                              {t(risk.type)} {risk.data && `- ${risk.data}`}
                            </span>
                            <div className="flex items-center gap-1">
                              <Button
                                data-id={risk.id}
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 hover:text-blue-500"
                                onClick={startEditRisk}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting} className="min-w-[120px]">
              {isSubmitting ? t('Loading..') : t('RISK.EDIT.SAVE_BUTTON.TEXT')}
            </Button>
          </div>
        </form>
      </Form>

      <AdditionalRiskModal
        currentRisk={editingRisk || undefined}
        onAddRisk={addRisk}
        onEditRisk={editRisk}
        onDeleteRisk={removeRisk}
        existingRisks={additionalRisks}
        open={isModalOpen}
        onOpenChange={handleModalClose}
      />
    </div>
  )
}
