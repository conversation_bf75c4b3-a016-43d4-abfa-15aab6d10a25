'use client'

import { useCallback } from 'react'

import type { UserProfile } from '@/modules/auth/types/auth-types'
import { RiskForm } from '@/modules/risk/components/risk-form'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'
import { useTranslations } from 'next-intl'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface HealthAndRisksStepProps {
  userProfile: UserProfile
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement, userProfile }: HealthAndRisksStepProps) => {
  const t = useTranslations()

  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep, saveStepDataOnly } =
    useDataManagement(clientDataManagement)

  // Use userProfile directly - no transformation needed
  const initialDataForRiskForm = userProfile

  // Debug log to check if userProfile is being passed correctly
  console.log('🔍 [HealthAndRisksStep] userProfile:', userProfile)
  console.log('🔍 [HealthAndRisksStep] initialDataForRiskForm:', initialDataForRiskForm)

  // Handle risk form completion - directly save the data
  const handleRiskFormComplete = useCallback(
    async (riskData: RiskFormInputs) => {
      // Add lastUpdate and save directly
      const healthAndRisksData = {
        ...riskData,
        lastUpdate: new Date().toISOString(),
      }

      // Save the data using the data management system
      await handleFormSubmission(healthAndRisksData)
    },
    [handleFormSubmission]
  )

  // Navigation handlers
  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  const handleSubmit = async () => {
    // For the health and risks step, we need to trigger the risk form submission
    // Since RiskForm doesn't expose its form submission directly, we'll need to handle this differently
    // For now, we'll just proceed to next step if data exists
    if (userProfile) {
      await handleFormSubmission(userProfile)
    }
  }

  return (
    <div className="space-y-6">
      {/* Risk Form Content */}
      <div className="space-y-6">
        <RiskForm initialData={initialDataForRiskForm} onComplete={handleRiskFormComplete} />
      </div>

      {/* Standard data management navigation */}
      <DataManagementNavigation
        submitButtonDisabled={false}
        nextStepDisabled={false}
        previousStepDisabled={isFirstStep}
        isSubmitting={isSaving}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        onNext={goToNextStep}
        showSubmitButton={isLastStep}
        clientDataManagement={clientDataManagement}
      />
    </div>
  )
}
