'use client'

import { useCallback, useMemo } from 'react'

import { RiskFormInputs } from '@/modules/risk/types/risk-schema'
import { useTranslations } from 'next-intl'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement, HealthAndRisksData } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'
import { RiskFormWrapper } from './risk-form-wrapper'

interface HealthAndRisksStepProps {
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement }: HealthAndRisksStepProps) => {
  const t = useTranslations()

  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep, saveStepDataOnly } =
    useDataManagement(clientDataManagement)

  // Get existing health and risks data if available
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  // Transform health and risks data to UserProfile format for RiskForm
  const initialDataForRiskForm = useMemo(() => {
    if (!existingHealthAndRisks) return null

    // Map smoker status from string to number (as expected by RiskForm)
    let smokerValue: number | undefined
    switch (existingHealthAndRisks.smoker) {
      case 'no':
        smokerValue = 0
        break
      case 'sometimes':
        smokerValue = 50
        break
      case 'yes':
        smokerValue = 100
        break
      default:
        smokerValue = undefined
    }

    return {
      weight: existingHealthAndRisks.weight ? Number(existingHealthAndRisks.weight) : undefined,
      height: existingHealthAndRisks.height ? Number(existingHealthAndRisks.height) : undefined,
      smoker: smokerValue,
      healthInfo: existingHealthAndRisks.healthInfo || '',
      additionalRisks: [], // TODO: Handle additional risks if needed
    }
  }, [existingHealthAndRisks])

  // Handle risk form completion - transform data back to HealthAndRisksData format
  const handleRiskFormComplete = useCallback(
    async (riskData: RiskFormInputs) => {
      // Transform RiskFormInputs back to HealthAndRisksData format
      let smokerString: string | undefined
      switch (riskData.smoker) {
        case 'NO':
          smokerString = 'no'
          break
        case 'SOMETIMES':
          smokerString = 'sometimes'
          break
        case 'YES':
          smokerString = 'yes'
          break
        default:
          smokerString = undefined
      }

      const healthAndRisksData: HealthAndRisksData = {
        weight: riskData.weight?.toString(),
        height: riskData.height?.toString(),
        smoker: smokerString,
        healthInfo: riskData.healthInfo,
        lastUpdate: new Date().toISOString(),
      }

      // Save the data using the data management system
      await handleFormSubmission(healthAndRisksData)
    },
    [handleFormSubmission]
  )

  // Navigation handlers
  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  const handleSubmit = async () => {
    // For the health and risks step, we need to trigger the risk form submission
    // Since RiskForm doesn't expose its form submission directly, we'll need to handle this differently
    // For now, we'll just proceed to next step if data exists
    if (existingHealthAndRisks) {
      await handleFormSubmission(existingHealthAndRisks)
    }
  }

  return (
    <div className="space-y-6">
      {/* Risk Form Content */}
      <div className="space-y-6">
        <RiskFormWrapper
          initialData={initialDataForRiskForm}
          onComplete={handleRiskFormComplete}
          isLoading={isSaving}
        />
      </div>

      {/* Standard data management navigation */}
      <DataManagementNavigation
        submitButtonDisabled={false}
        nextStepDisabled={false}
        previousStepDisabled={isFirstStep}
        isSubmitting={isSaving}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        onNext={goToNextStep}
        showSubmitButton={isLastStep}
        clientDataManagement={clientDataManagement}
      />
    </div>
  )
}
