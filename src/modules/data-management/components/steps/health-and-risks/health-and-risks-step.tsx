'use client'

import { useCallback, useMemo } from 'react'

import type { UserProfile } from '@/modules/auth/types/auth-types'
import { RiskForm } from '@/modules/risk/components/risk-form'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'
import { useTranslations } from 'next-intl'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface HealthAndRisksStepProps {
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement }: HealthAndRisksStepProps) => {
  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep } =
    useDataManagement(clientDataManagement)

  // Get existing health and risks data from data management
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  // Use existing data if available, otherwise null
  const initialDataForRiskForm = existingHealthAndRisks || null

  // Check if step has been updated (similar to contracts step)
  const isStepUpdated = useMemo(() => {
    return !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate)
  }, [existingHealthAndRisks])

  // Determine if next button should be disabled
  const canGoNext = isStepUpdated

  console.log('🔍 [HealthAndRisksStep] Navigation state:', {
    existingHealthAndRisks,
    isStepUpdated,
    canGoNext,
    hasLastUpdate: !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate),
  })

  // Handle risk form completion - directly save the data
  const handleRiskFormComplete = useCallback(
    async (riskData: RiskFormInputs) => {
      // Add lastUpdate and save directly
      const healthAndRisksData = {
        ...riskData,
        lastUpdate: new Date().toISOString(),
      }

      // Save the data using the data management system
      await handleFormSubmission(healthAndRisksData)
    },
    [handleFormSubmission]
  )

  // Navigation handlers
  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  const handleSubmit = async () => {
    // For the health and risks step, we need to trigger the risk form submission
    // Since RiskForm doesn't expose its form submission directly, we'll need to handle this differently
    // For now, we'll just proceed to next step if data exists
    if (existingHealthAndRisks) {
      await handleFormSubmission(existingHealthAndRisks)
    }
  }

  return (
    <div className="space-y-6">
      {/* Risk Form Content */}
      <div className="space-y-6">
        <RiskForm
          initialData={initialDataForRiskForm}
          onComplete={handleRiskFormComplete}
          formWrapperClassName="max-w-none"
          isLoading={isSaving}
        />
      </div>

      {/* Standard data management navigation */}
      <DataManagementNavigation
        submitButtonDisabled={!canGoNext}
        nextStepDisabled={!canGoNext}
        previousStepDisabled={isFirstStep}
        isSubmitting={isSaving}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        onNext={goToNextStep}
        showSubmitButton={isLastStep}
        clientDataManagement={clientDataManagement}
      />
    </div>
  )
}
