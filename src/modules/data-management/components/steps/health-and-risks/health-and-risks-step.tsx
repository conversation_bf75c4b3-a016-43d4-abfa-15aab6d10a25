'use client'

import { useCallback, useMemo } from 'react'

import type { UserProfile } from '@/modules/auth/types/auth-types'
import { RiskForm } from '@/modules/risk/components/risk-form'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'
import { useTranslations } from 'next-intl'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface HealthAndRisksStepProps {
  userProfile: UserProfile
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement }: HealthAndRisksStepProps) => {
  const t = useTranslations()

  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep, saveStepDataOnly } =
    useDataManagement(clientDataManagement)

  // Get existing health and risks data if available
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  // Transform health and risks data to UserProfile format for RiskForm
  const initialDataForRiskForm = useMemo(() => {
    if (!existingHealthAndRisks) return null

    // Map smoker status to number (as expected by RiskForm)
    let smokerValue: number | undefined
    switch (existingHealthAndRisks.smoker) {
      case 'NO':
        smokerValue = 0
        break
      case 'SOMETIMES':
        smokerValue = 50
        break
      case 'YES':
        smokerValue = 100
        break
      default:
        smokerValue = undefined
    }

    return {
      weight: existingHealthAndRisks.weight,
      height: existingHealthAndRisks.height,
      smoker: smokerValue,
      healthInfo: existingHealthAndRisks.healthInfo || '',
      additionalRisks: existingHealthAndRisks.additionalRisks || [],
    }
  }, [existingHealthAndRisks])

  // Handle risk form completion - directly save the data
  const handleRiskFormComplete = useCallback(
    async (riskData: RiskFormInputs) => {
      // Add lastUpdate and save directly
      const healthAndRisksData = {
        ...riskData,
        lastUpdate: new Date().toISOString(),
      }

      // Save the data using the data management system
      await handleFormSubmission(healthAndRisksData)
    },
    [handleFormSubmission]
  )

  // Navigation handlers
  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  const handleSubmit = async () => {
    // For the health and risks step, we need to trigger the risk form submission
    // Since RiskForm doesn't expose its form submission directly, we'll need to handle this differently
    // For now, we'll just proceed to next step if data exists
    if (existingHealthAndRisks) {
      await handleFormSubmission(existingHealthAndRisks)
    }
  }

  return (
    <div className="space-y-6">
      {/* Risk Form Content */}
      <div className="space-y-6">
        <RiskForm initialData={initialDataForRiskForm} onComplete={handleRiskFormComplete} />
      </div>

      {/* Standard data management navigation */}
      <DataManagementNavigation
        submitButtonDisabled={false}
        nextStepDisabled={false}
        previousStepDisabled={isFirstStep}
        isSubmitting={isSaving}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        onNext={goToNextStep}
        showSubmitButton={isLastStep}
        clientDataManagement={clientDataManagement}
      />
    </div>
  )
}
