'use client'

import { useCallback, useMemo, useRef } from 'react'

import type { UserProfile } from '@/modules/auth/types/auth-types'
import { RiskForm } from '@/modules/risk/components/risk-form'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'
import { useTranslations } from 'next-intl'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface HealthAndRisksStepProps {
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement }: HealthAndRisksStepProps) => {
  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep } =
    useDataManagement(clientDataManagement)

  // Ref to access RiskForm methods
  const riskFormRef = useRef<{ validateAndGetData: () => Promise<RiskFormInputs | null> }>(null)

  // Get existing health and risks data from data management
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  // Use existing data if available, otherwise null
  const initialDataForRiskForm = existingHealthAndRisks || null

  // Check if step has been updated (similar to contracts step)
  const isStepUpdated = useMemo(() => {
    return !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate)
  }, [existingHealthAndRisks])

  // Determine if next button should be disabled
  const canGoNext = isStepUpdated

  console.log('🔍 [HealthAndRisksStep] Navigation state:', {
    existingHealthAndRisks,
    isStepUpdated,
    canGoNext,
    hasLastUpdate: !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate),
  })

  // Navigation handlers
  const handlePrevious = useCallback(async () => {
    await goToPreviousStep()
  }, [goToPreviousStep])

  const handleNext = useCallback(async () => {
    // Validate and get data from RiskForm
    const formData = await riskFormRef.current?.validateAndGetData()

    if (formData) {
      // Add lastUpdate and save the data
      const healthAndRisksData = {
        ...formData,
        lastUpdate: new Date().toISOString(),
      }

      // Save and proceed to next step
      await handleFormSubmission(healthAndRisksData)
    }
  }, [handleFormSubmission])

  const handleSubmit = useCallback(async () => {
    // Same logic as handleNext for final submission
    const formData = await riskFormRef.current?.validateAndGetData()

    if (formData) {
      const healthAndRisksData = {
        ...formData,
        lastUpdate: new Date().toISOString(),
      }

      await handleFormSubmission(healthAndRisksData)
    }
  }, [handleFormSubmission])

  return (
    <div className="space-y-6">
      {/* Risk Form Content */}
      <div className="space-y-6">
        <RiskForm
          ref={riskFormRef}
          initialData={initialDataForRiskForm}
          formWrapperClassName="max-w-none"
          isLoading={isSaving}
        />
      </div>

      {/* Standard data management navigation */}
      <DataManagementNavigation
        submitButtonDisabled={!canGoNext}
        nextStepDisabled={!canGoNext}
        previousStepDisabled={isFirstStep}
        isSubmitting={isSaving}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        onNext={goToNextStep}
        showSubmitButton={isLastStep}
        clientDataManagement={clientDataManagement}
      />
    </div>
  )
}
